import 'package:flutter/material.dart';
import 'package:insight/l10n/app_localizations.dart';
import '../widgets/behavior_score_widget.dart';
import '../widgets/info_note_widget.dart';
import '../widgets/performance_comparison_widget.dart';
import '../widgets/simple_performance_chart.dart';
import '../widgets/staff_comparison_chart.dart';
import '../widgets/away_time_widget.dart';
import '../widgets/frame_snapshots_widget.dart';

import '../widgets/download_report_widget.dart';
import '../services/ai_api_service.dart';
import 'staff_screen.dart';

class EmployeeProfileScreen extends StatefulWidget {
  final StaffMember staffMember;
  final String userRole;

  const EmployeeProfileScreen({
    Key? key,
    required this.staffMember,
    required this.userRole,
  }) : super(key: key);

  @override
  State<EmployeeProfileScreen> createState() => _EmployeeProfileScreenState();
}

class _EmployeeProfileScreenState extends State<EmployeeProfileScreen> {
  final AiApiService _aiApiService = AiApiService();
  int? _productivityScore;
  bool _isLoadingProductivity = true;
  String? _productivityError;

  @override
  void initState() {
    super.initState();
    _loadProductivityData();
  }

  List<String> _getSnapshotImagesForEmployee() {
    // Check if the employee is Talha and return static images
    if (widget.staffMember.name.toLowerCase() == 'talha') {
      return [
        'assets/talha1.jpg',
        'assets/talha2.jpg',
        'assets/talha3.jpg',
      ];
    }
    // Check if the employee is Ali and return static images
    else if (widget.staffMember.name.toLowerCase() == 'ali') {
      return [
        'assets/ali1.jpg',
        'assets/ali2.jpg',
        'assets/ali3.jpg',
      ];
    }
    // Check if the employee is Basit and return static images
    else if (widget.staffMember.name.toLowerCase() == 'basit') {
      return [
        'assets/basit1.jpg',
        'assets/basit2.jpg',
        'assets/basit3.jpg',
      ];
    }
    // Return empty list for other employees (will show "No Image")
    return [];
  }

  Future<void> _loadProductivityData() async {
    try {
      setState(() {
        _isLoadingProductivity = true;
        _productivityError = null;
      });

      print('🔍 Fetching productivity for employee: ${widget.staffMember.name}');

      final response = await _aiApiService.getAllEmployeesProductivity();

      print('📊 Productivity API response: $response');

      // Find the specific employee's data
      if (response['employees'] != null && response['employees'] is List) {
        final employees = List<Map<String, dynamic>>.from(response['employees']);

        // Look for the employee by name (case-insensitive)
        final employeeData = employees.firstWhere(
          (emp) => emp['employee']?.toString().toLowerCase() == widget.staffMember.name.toLowerCase(),
          orElse: () => <String, dynamic>{},
        );

        if (employeeData.isNotEmpty && employeeData['productivity_score'] != null) {
          final score = employeeData['productivity_score'];
          final calculatedScore = (score is num) ? score.round().clamp(0, 100) : null;

          print('✅ Productivity score found for ${widget.staffMember.name}: $score -> $calculatedScore%');

          setState(() {
            _productivityScore = calculatedScore;
            _isLoadingProductivity = false;
          });
        } else {
          print('❌ No productivity data found for employee: ${widget.staffMember.name}');
          print('Available employees: ${employees.map((e) => e['employee']).toList()}');
          setState(() {
            _productivityScore = null;
            _isLoadingProductivity = false;
            _productivityError = 'No productivity data available for ${widget.staffMember.name}';
          });
        }
      } else {
        print('❌ No employees array in response');
        setState(() {
          _productivityScore = null;
          _isLoadingProductivity = false;
          _productivityError = 'Invalid productivity data format';
        });
      }
    } catch (e) {
      print('💥 Error loading productivity data: $e');
      setState(() {
        _productivityScore = null;
        _isLoadingProductivity = false;
        _productivityError = 'Failed to load productivity data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(_getResponsiveAppBarHeight(context)),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: false, // Remove default leading
          centerTitle: false,
          flexibleSpace: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: _getResponsiveContentPadding(context),
                bottom: 8.0,
              ),
              child: Row(
                children: [
                  // Back Button - Aligned with profile image
                  IconButton(
                    icon: const Icon(
                      Icons.chevron_left,
                      color: Colors.black,
                      size: 28,
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  SizedBox(width: _getResponsiveBackButtonSpacing(context)),

                  // Profile Picture - Responsive size
                  CircleAvatar(
                    radius: _getResponsiveImageRadius(context),
                    backgroundColor: const Color(0xFFE5E7EB),
                    backgroundImage: AssetImage(widget.staffMember.avatar),
                    child: widget.staffMember.avatar.isEmpty
                        ? Text(
                            widget.staffMember.name
                                .split(' ')
                                .map((name) => name[0])
                                .join('')
                                .toUpperCase(),
                            style: TextStyle(
                              fontSize:
                                  _getResponsiveImageRadius(context) * 0.5,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF6B7280),
                            ),
                          )
                        : null,
                  ),
                  SizedBox(width: _getResponsiveSpacing(context)),

                  // Name and Role
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.staffMember.name,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: _getResponsiveNameFontSize(context),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: _getResponsiveTextSpacing(context)),
                        Text(
                          widget.staffMember.getTranslatedRole(context),
                          style: TextStyle(
                            color: const Color(0xFF4B5563),
                            fontSize: _getResponsiveRoleFontSize(context),
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Behavior Score
            BehaviorScoreWidget(
              productivityScore: widget.staffMember.score,
              attendanceScore: widget.staffMember.safeAttendanceRate,
              realProductivityScore: _productivityScore,
              isLoadingProductivity: _isLoadingProductivity,
            ),
            const SizedBox(height: 16),

            // Info Note - Outside behavior score
            InfoNoteWidget(
              message: localizations.moreProductiveThanAverage(
                widget.staffMember.name.split(' ').first,
                '${(widget.staffMember.score - 50).abs()}', // Calculate difference from average (50%)
              ),
            ),
            const SizedBox(height: 16),

            // Performance Comparison
            PerformanceComparisonWidget(
              avgDailyScore: widget.staffMember.score,
              workTimePercentage: widget
                  .staffMember
                  .score, // Using behavior score as work time percentage
            ),
            const SizedBox(height: 16),

            // Employee's Performance Chart
            SimplePerformanceChart(
              title:
                  "${widget.staffMember.name.split(' ').first}'s ${localizations.performance}",
              data: const [60, 85, 70, 45, 30, 90, 75], // Fallback data
              employeeName: widget.staffMember.name,
            ),
            const SizedBox(height: 16),

            // Compare Staff Chart
            StaffComparisonChart(
              title: localizations.compareStaff,
              data: const [50, 72, 93, 57, 29, 91, 75],
              avatars: const [
                'assets/sk.png',
                'assets/jane.png',
                'assets/sarah.png',
                'assets/sk.png',
                'assets/jane.png',
                'assets/sarah.png',
                'assets/sk.png',
              ],
            ),
            const SizedBox(height: 16),

            // Away Time
            AwayTimeWidget(
              title: localizations.awayTime,
              description: localizations.awayFromZone(
                widget.staffMember.name.split(' ').first,
                '30 ${localizations.minutes}',
              ),
              duration:
                  '${localizations.forDuration} 30 ${localizations.minutes}',
              additionalInfo: '8 ${localizations.viewCameraLive}',
            ),
            const SizedBox(height: 16),

            // Frame Snapshots
            FrameSnapshotsWidget(
              title: localizations.frameSnapshots,
              description: localizations.capturedFromFootage,
              snapshotImages: _getSnapshotImagesForEmployee(),
            ),
            const SizedBox(height: 16),



            // Download Report - Only show for admin users
            if (widget.userRole.toLowerCase() == 'admin') ...[
              DownloadReportWidget(
                employeeName: widget.staffMember.name,
              ),
              const SizedBox(height: 20),
            ] else ...[
              const SizedBox(height: 20),
            ],
          ],
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getResponsiveAppBarHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 100.0; // Increased height for small screens
    } else if (screenWidth < 400) {
      return 110.0; // Increased height for medium screens
    } else {
      return 120.0; // Increased height for larger screens
    }
  }

  double _getResponsiveBackButtonSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 8.0;
    } else if (screenWidth < 400) {
      return 12.0;
    } else {
      return 16.0;
    }
  }

  double _getResponsiveContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0;
    } else if (screenWidth < 400) {
      return 20.0;
    } else {
      return 24.0;
    }
  }

  double _getResponsiveImageRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 28.0; // 56px diameter for small screens
    } else if (screenWidth < 400) {
      return 30.0; // 60px diameter for medium screens
    } else {
      return 32.0; // 64px diameter for larger screens
    }
  }

  double _getResponsiveSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 12.0;
    } else if (screenWidth < 400) {
      return 14.0;
    } else {
      return 16.0;
    }
  }

  double _getResponsiveNameFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0;
    } else if (screenWidth < 400) {
      return 17.0;
    } else {
      return 18.0;
    }
  }

  double _getResponsiveRoleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 11.0;
    } else if (screenWidth < 400) {
      return 11.5;
    } else {
      return 12.0;
    }
  }

  double _getResponsiveTextSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 2.0;
    } else if (screenWidth < 400) {
      return 3.0;
    } else {
      return 4.0;
    }
  }
}

{"name": "insight_app_backend", "version": "1.0.0", "main": "./src/server.js", "scripts": {"dev": "nodemon ./src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky", "sync": "node ./src/scripts/envSync.js", "migrate-profile-source": "node ./src/scripts/migrateProfilePictureSource.js", "list-admins": "node ./src/scripts/listAdmins.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10"}, "devDependencies": {"husky": "^9.1.7"}}